<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Delete Button</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        button { padding: 10px 20px; margin: 10px 0; background: #42b983; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #369870; }
        .pet-item { background: #f0f0f0; padding: 10px; margin: 5px 0; border-radius: 4px; }
        .delete-btn { background: #e74c3c; color: white; padding: 5px 10px; margin-left: 10px; }
    </style>
</head>
<body>
    <h1>Debug: <PERSON><PERSON><PERSON>r</h1>
    <p>Este script diagnostica por qué el botón de borrar no está funcionando.</p>
    
    <button onclick="testAuth()">1. Verificar Autenticación</button>
    <button onclick="loadPets()">2. Cargar Mascotas</button>
    <button onclick="testDeleteFunction()">3. Probar Función de Borrado</button>
    <button onclick="clearLogs()">Limpiar Logs</button>
    
    <div id="logs"></div>
    <div id="pets-list"></div>
    
    <script type="module">
        import { supabase } from './src/services/supabase.js';
        
        const logsDiv = document.getElementById('logs');
        const petsListDiv = document.getElementById('pets-list');
        let currentUser = null;
        let currentPets = [];
        
        function addLog(message, type = 'log') {
            const logElement = document.createElement('div');
            logElement.className = `log ${type}`;
            logElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logElement);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }
        
        window.clearLogs = function() {
            logsDiv.innerHTML = '';
            petsListDiv.innerHTML = '';
        }
        
        window.testAuth = async function() {
            addLog('🔍 Verificando autenticación...', 'log');
            
            try {
                const { data: session, error } = await supabase.auth.getSession();
                
                if (error) {
                    addLog(`❌ Error al obtener sesión: ${error.message}`, 'error');
                    return;
                }
                
                if (!session || !session.session) {
                    addLog('❌ No hay sesión activa', 'error');
                    addLog('💡 Necesitas iniciar sesión en la aplicación principal primero', 'warning');
                    return;
                }
                
                currentUser = session.session.user;
                addLog(`✅ Usuario autenticado: ${currentUser.email}`, 'success');
                addLog(`📋 ID de usuario: ${currentUser.id}`, 'log');
                
            } catch (error) {
                addLog(`💥 Error general: ${error.message}`, 'error');
            }
        }
        
        window.loadPets = async function() {
            if (!currentUser) {
                addLog('⚠️ Primero verifica la autenticación', 'warning');
                return;
            }
            
            addLog('🔍 Cargando mascotas...', 'log');
            
            try {
                const { data, error } = await supabase
                    .from('pets')
                    .select('*')
                    .eq('owner_id', currentUser.id)
                    .order('created_at', { ascending: true });
                
                if (error) {
                    addLog(`❌ Error al cargar mascotas: ${error.message}`, 'error');
                    return;
                }
                
                currentPets = data || [];
                addLog(`✅ Mascotas cargadas: ${currentPets.length}`, 'success');
                
                if (currentPets.length === 0) {
                    addLog('📝 No hay mascotas para este usuario', 'warning');
                    petsListDiv.innerHTML = '<p>No hay mascotas para probar el borrado.</p>';
                } else {
                    displayPets();
                }
                
            } catch (error) {
                addLog(`💥 Error general: ${error.message}`, 'error');
            }
        }
        
        function displayPets() {
            const petsHtml = currentPets.map(pet => `
                <div class="pet-item">
                    <strong>${pet.name}</strong> (${pet.type}) - ID: ${pet.id}
                    <button class="delete-btn" onclick="testDeletePet('${pet.id}', '${pet.name}')">🗑️ Borrar</button>
                </div>
            `).join('');
            
            petsListDiv.innerHTML = `<h3>Mascotas disponibles:</h3>${petsHtml}`;
        }
        
        window.testDeletePet = async function(petId, petName) {
            addLog(`🔄 Iniciando borrado de ${petName} (ID: ${petId})...`, 'log');
            
            try {
                // Paso 1: Borrar interacciones
                addLog('📝 Paso 1: Borrando interacciones...', 'log');
                const { data: deletedInteractions, error: intError } = await supabase
                    .from('interactions')
                    .delete()
                    .eq('pet_id', petId)
                    .select();
                
                if (intError) {
                    addLog(`❌ Error al borrar interacciones: ${intError.message}`, 'error');
                } else {
                    addLog(`✅ Interacciones borradas: ${deletedInteractions?.length || 0}`, 'success');
                }
                
                // Paso 2: Borrar mensajes
                addLog('📝 Paso 2: Borrando mensajes...', 'log');
                const { data: deletedMessages, error: msgError } = await supabase
                    .from('messages')
                    .delete()
                    .eq('pet_id', petId)
                    .select();
                
                if (msgError) {
                    addLog(`❌ Error al borrar mensajes: ${msgError.message}`, 'error');
                } else {
                    addLog(`✅ Mensajes borrados: ${deletedMessages?.length || 0}`, 'success');
                }
                
                // Paso 3: Borrar mascota
                addLog('📝 Paso 3: Borrando mascota...', 'log');
                const { data: deletedPet, error: petError } = await supabase
                    .from('pets')
                    .delete()
                    .eq('id', petId)
                    .select();
                
                if (petError) {
                    addLog(`❌ Error al borrar mascota: ${petError.message}`, 'error');
                    addLog(`📋 Código de error: ${petError.code}`, 'warning');
                    addLog(`📋 Detalles: ${petError.details || 'N/A'}`, 'warning');
                } else {
                    addLog(`✅ Mascota borrada exitosamente: ${deletedPet?.length || 0}`, 'success');
                    
                    // Recargar la lista
                    await loadPets();
                }
                
            } catch (error) {
                addLog(`💥 Error general al borrar: ${error.message}`, 'error');
            }
        }
        
        window.testDeleteFunction = async function() {
            addLog('🔍 Probando función RPC de borrado...', 'log');
            
            try {
                // Probar función principal
                const { data: data1, error: error1 } = await supabase
                    .rpc('delete_pet_with_related_data', { 
                        target_pet_id: '00000000-0000-0000-0000-000000000000' 
                    });
                
                if (error1) {
                    addLog(`❌ Error en función principal: ${error1.message}`, 'error');
                } else {
                    addLog(`✅ Función principal funciona: ${data1}`, 'success');
                }
                
                // Probar función wrapper
                const { data: data2, error: error2 } = await supabase
                    .rpc('delete_pet_with_related_data_wrapper', { 
                        pet_id_param: '00000000-0000-0000-0000-000000000000' 
                    });
                
                if (error2) {
                    addLog(`❌ Error en función wrapper: ${error2.message}`, 'error');
                } else {
                    addLog(`✅ Función wrapper funciona: ${data2}`, 'success');
                }
                
            } catch (error) {
                addLog(`💥 Error al probar funciones RPC: ${error.message}`, 'error');
            }
        }
        
        // Auto-ejecutar verificación de auth al cargar
        addLog('🚀 Herramienta de diagnóstico cargada', 'log');
        addLog('📝 Paso 1: Haz clic en "Verificar Autenticación"', 'log');
    </script>
</body>
</html>
