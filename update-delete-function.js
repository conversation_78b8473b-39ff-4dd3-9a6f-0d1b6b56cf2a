// Script para actualizar la función de borrado en Supabase
import { supabase } from './src/services/supabase.js';
import { readFileSync } from 'fs';

async function updateDeleteFunction() {
  console.log('Actualizando función de borrado en Supabase...');
  
  try {
    // Leer el archivo SQL
    const sqlContent = readFileSync('./sql/create_delete_pet_function.sql', 'utf8');
    console.log('Contenido SQL:', sqlContent);
    
    // Ejecutar la función SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlContent });
    
    if (error) {
      console.error('Error al ejecutar SQL:', error);
      
      // Intentar método alternativo - crear la función directamente
      console.log('Intentando método alternativo...');
      
      const { data: altData, error: altError } = await supabase
        .rpc('delete_pet_with_related_data', { pet_id_param: '00000000-0000-0000-0000-000000000000' });
      
      if (altError && altError.message.includes('function delete_pet_with_related_data')) {
        console.log('La función no existe. Necesitas ejecutar el SQL manualmente en Supabase.');
        console.log('SQL a ejecutar:');
        console.log(sqlContent);
      } else {
        console.log('La función parece existir pero con parámetros antiguos');
      }
    } else {
      console.log('Función actualizada exitosamente:', data);
    }
    
  } catch (error) {
    console.error('Error general:', error);
  }
}

// Función para probar la función de borrado
async function testDeleteFunction() {
  console.log('Probando función de borrado...');
  
  try {
    const { data, error } = await supabase
      .rpc('delete_pet_with_related_data', { pet_id_param: '00000000-0000-0000-0000-000000000000' });
    
    if (error) {
      console.error('Error en función de borrado:', error);
    } else {
      console.log('Función de borrado funciona correctamente:', data);
    }
  } catch (error) {
    console.error('Error al probar función:', error);
  }
}

// Ejecutar
updateDeleteFunction().then(() => {
  return testDeleteFunction();
});
