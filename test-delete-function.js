// Script de prueba para verificar la función de borrado
import { supabase } from './src/services/supabase.js';

async function testDeleteFunction() {
  console.log('Probando la función de borrado...');
  
  try {
    // Verificar que podemos conectar a Supabase
    const { data: session } = await supabase.auth.getSession();
    console.log('Sesión actual:', session);
    
    // Probar si la función RPC existe
    const { data, error } = await supabase
      .rpc('delete_pet_with_related_data', { pet_id: '00000000-0000-0000-0000-000000000000' });
    
    if (error) {
      console.error('Error al probar la función RPC:', error);
      if (error.message.includes('function delete_pet_with_related_data')) {
        console.log('La función RPC no existe en la base de datos');
      }
    } else {
      console.log('La función RPC existe y responde:', data);
    }
    
    // Probar borrado directo
    console.log('Probando borrado directo...');
    const { error: directError } = await supabase
      .from('pets')
      .delete()
      .eq('id', '00000000-0000-0000-0000-000000000000');
    
    if (directError) {
      console.log('Error en borrado directo (esperado para ID inexistente):', directError);
    } else {
      console.log('Borrado directo funciona');
    }
    
  } catch (error) {
    console.error('Error general:', error);
  }
}

// Ejecutar solo si se llama directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  testDeleteFunction();
}

export { testDeleteFunction };
