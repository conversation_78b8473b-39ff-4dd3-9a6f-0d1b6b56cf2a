<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test RPC Function</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        button { padding: 10px 20px; margin: 10px 0; background: #42b983; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #369870; }
    </style>
</head>
<body>
    <h1>Test de Función RPC de Borrado</h1>
    <p>Este script prueba si la función RPC está funcionando correctamente.</p>
    
    <button onclick="testRPCFunction()">Probar Función RPC</button>
    <button onclick="clearLogs()">Limpiar Logs</button>
    
    <div id="logs"></div>
    
    <script type="module">
        import { supabase } from './src/services/supabase.js';

        const logsDiv = document.getElementById('logs');

        function addLog(message, type = 'log') {
            const logElement = document.createElement('div');
            logElement.className = `log ${type}`;
            logElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logElement);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            logsDiv.innerHTML = '';
        }

        async function testRPCFunction() {
            addLog('🔄 Iniciando test de función RPC...', 'log');
            
            try {
                // Test 1: Probar con target_pet_id (función principal)
                addLog('📝 Test 1: Probando con target_pet_id (función principal)...', 'log');
                const { data: data1, error: error1 } = await supabase
                    .rpc('delete_pet_with_related_data', {
                        target_pet_id: '00000000-0000-0000-0000-000000000000'
                    });

                if (error1) {
                    addLog(`❌ Error con target_pet_id: ${error1.message}`, 'error');
                    addLog(`📋 Código de error: ${error1.code}`, 'warning');
                } else {
                    addLog(`✅ Test 1 exitoso con target_pet_id: ${data1}`, 'success');
                }

                // Test 2: Probar con función wrapper
                addLog('📝 Test 2: Probando función wrapper...', 'log');
                const { data: data2, error: error2 } = await supabase
                    .rpc('delete_pet_with_related_data_wrapper', {
                        pet_id_param: '00000000-0000-0000-0000-000000000000'
                    });

                if (error2) {
                    addLog(`❌ Error con función wrapper: ${error2.message}`, 'error');
                } else {
                    addLog(`✅ Test 2 exitoso con función wrapper: ${data2}`, 'success');
                }

                // Test 3: Probar con pet_id (función antigua)
                addLog('📝 Test 3: Probando con pet_id (función antigua)...', 'log');
                const { data: data3, error: error3 } = await supabase
                    .rpc('delete_pet_with_related_data', {
                        pet_id: '00000000-0000-0000-0000-000000000000'
                    });

                if (error3) {
                    addLog(`❌ Error con pet_id: ${error3.message}`, 'error');
                } else {
                    addLog(`✅ Test 3 exitoso con pet_id: ${data3}`, 'success');
                }
                
                // Resumen
                if (!error1) {
                    addLog('🎉 ¡Función principal funcionando correctamente!', 'success');
                    addLog('✅ Los botones de borrar deberían funcionar ahora.', 'success');
                } else if (!error2) {
                    addLog('🎉 ¡Función wrapper funcionando correctamente!', 'success');
                    addLog('✅ Los botones de borrar deberían funcionar ahora.', 'success');
                } else if (!error3) {
                    addLog('⚠️ Solo la función antigua funciona (con problemas de ambigüedad)', 'warning');
                    addLog('💡 Ejecuta el SQL actualizado en Supabase.', 'warning');
                } else {
                    addLog('❌ Ninguna función RPC funciona', 'error');
                    addLog('🔧 Ejecuta el SQL completo en Supabase para crear las funciones.', 'warning');
                    addLog('💡 Pero no te preocupes: el borrado manual en cascada debería funcionar.', 'log');
                }
                
            } catch (error) {
                addLog(`💥 Error general: ${error.message}`, 'error');
            }
        }
        
        // Hacer las funciones globales
        window.testRPCFunction = testRPCFunction;
        window.clearLogs = clearLogs;

        // Auto-ejecutar al cargar
        addLog('🚀 Página cargada. Haz clic en "Probar Función RPC" para comenzar.', 'log');
    </script>
</body>
</html>
