<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test RPC Function</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        button { padding: 10px 20px; margin: 10px 0; background: #42b983; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #369870; }
    </style>
</head>
<body>
    <h1>Test de Función RPC de Borrado</h1>
    <p>Este script prueba si la función RPC está funcionando correctamente.</p>
    
    <button onclick="testRPCFunction()">Probar Función RPC</button>
    <button onclick="clearLogs()">Limpiar Logs</button>
    
    <div id="logs"></div>
    
    <script type="module">
        import { supabase } from './src/services/supabase.js';
        
        const logsDiv = document.getElementById('logs');
        
        function addLog(message, type = 'log') {
            const logElement = document.createElement('div');
            logElement.className = `log ${type}`;
            logElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logElement);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }
        
        window.clearLogs = function() {
            logsDiv.innerHTML = '';
        }
        
        window.testRPCFunction = async function() {
            addLog('🔄 Iniciando test de función RPC...', 'log');
            
            try {
                // Test 1: Probar con pet_id_param (nuevo parámetro)
                addLog('📝 Test 1: Probando con pet_id_param...', 'log');
                const { data: data1, error: error1 } = await supabase
                    .rpc('delete_pet_with_related_data', { 
                        pet_id_param: '00000000-0000-0000-0000-000000000000' 
                    });
                
                if (error1) {
                    addLog(`❌ Error con pet_id_param: ${error1.message}`, 'error');
                    addLog(`📋 Código de error: ${error1.code}`, 'warning');
                    
                    if (error1.message.includes('function delete_pet_with_related_data')) {
                        addLog('⚠️ La función RPC no existe. Necesitas ejecutar el SQL en Supabase.', 'warning');
                        return;
                    }
                } else {
                    addLog(`✅ Test 1 exitoso con pet_id_param: ${data1}`, 'success');
                }
                
                // Test 2: Probar con pet_id (parámetro antiguo)
                addLog('📝 Test 2: Probando con pet_id (parámetro antiguo)...', 'log');
                const { data: data2, error: error2 } = await supabase
                    .rpc('delete_pet_with_related_data', { 
                        pet_id: '00000000-0000-0000-0000-000000000000' 
                    });
                
                if (error2) {
                    addLog(`❌ Error con pet_id: ${error2.message}`, 'error');
                } else {
                    addLog(`✅ Test 2 exitoso con pet_id: ${data2}`, 'success');
                }
                
                // Resumen
                if (!error1) {
                    addLog('🎉 ¡Función RPC funcionando correctamente con pet_id_param!', 'success');
                    addLog('✅ Los botones de borrar deberían funcionar ahora.', 'success');
                } else if (!error2) {
                    addLog('⚠️ Función RPC funciona solo con pet_id (parámetro antiguo)', 'warning');
                    addLog('💡 Considera actualizar la función SQL en Supabase.', 'warning');
                } else {
                    addLog('❌ Función RPC no funciona con ningún parámetro', 'error');
                    addLog('🔧 Ejecuta el SQL en Supabase para crear/actualizar la función.', 'warning');
                }
                
            } catch (error) {
                addLog(`💥 Error general: ${error.message}`, 'error');
            }
        }
        
        // Auto-ejecutar al cargar
        addLog('🚀 Página cargada. Haz clic en "Probar Función RPC" para comenzar.', 'log');
    </script>
</body>
</html>
