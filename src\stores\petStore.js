import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { supabase } from '../services/supabase';
import { useAuthStore } from './authStore';

export const PetType = {
  CAT: 'cat',
  DOG: 'dog',
  BUNNY: 'bunny'
};

export const usePetStore = defineStore('pet', () => {
  const pet = ref(null);
  const pets = ref([]);
  const messages = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const lastSave = ref(null);
  let autoSaveInterval = null;

  const auth = useAuthStore();

  const mood = computed(() => {
    if (!pet.value) return 'neutral';
    if (pet.value.hunger > 70) return 'hungry';
    if (pet.value.energy < 30) return 'sleepy';
    if (pet.value.cleanliness < 30) return 'dirty';
    if (pet.value.happiness > 70) return 'happy';
    if (pet.value.happiness < 30) return 'sad';
    return 'neutral';
  });

  function initAutoSave() {
    if (autoSaveInterval) clearInterval(autoSaveInterval);
    autoSaveInterval = setInterval(() => {
      if (pet.value) savePetState();
    }, 5 * 60 * 1000);
  }

  function stopAutoSave() {
    if (autoSaveInterval) {
      clearInterval(autoSaveInterval);
      autoSaveInterval = null;
    }
  }

  async function createPet(name, type, ownerId) {
    try {
      loading.value = true;
      error.value = null;
      const newPet = {
        name,
        type,
        owner_id: ownerId,
        happiness: 50,
        hunger: 20,
        energy: 80,
        cleanliness: 80,
        health: 100,
        last_interaction: new Date().toISOString()
      };
      const { data, error: err } = await supabase
        .from('pets')
        .insert(newPet)
        .select()
        .single();
      if (err) throw err;
      pet.value = data;
      initAutoSave();
      await logInteraction('create', 'Pet created');
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  }

  async function getPetByOwnerId(ownerId) {
    try {
      loading.value = true;
      error.value = null;
      const { data, error: err } = await supabase
        .from('pets')
        .select('*')
        .eq('owner_id', ownerId)
        .single();
      if (err) {
        if (err.code === 'PGRST116') return null;
        throw err;
      }
      pet.value = data;
      initAutoSave();
      await loadMessages();
      return pet.value;
    } catch (err) {
      error.value = err.message;
      return null;
    } finally {
      loading.value = false;
    }
  }

  async function fetchPets(userId) {
    const ownerId = userId || (auth.user && auth.user.id);
    if (!supabase || !ownerId) return;
    
    console.log('Fetching pets for owner:', ownerId);
    
    try {
      loading.value = true;
      error.value = null;
      
      // Crear una nueva consulta desde cero
      let query = supabase
        .from('pets')
        .select('*')
        .eq('owner_id', ownerId)
        .order('created_at', { ascending: true });
      
      // Ejecutar la consulta
      const { data, error: err } = await query;
      
      if (err) {
        console.error('Supabase error:', err);
        throw err;
      }
      
      console.log('Pets fetched:', data);
      pets.value = data || [];
      return pets.value;
    } catch (err) {
      console.error('Error in fetchPets:', err);
      error.value = err.message;
      pets.value = [];
      return [];
    } finally {
      loading.value = false;
    }
  }

  async function setCurrentPet(petObj) {
    pet.value = petObj;
    if (petObj) {
      initAutoSave();
      await loadMessages();
    }
  }

  async function savePetState() {
    if (!pet.value) return;
    try {
      const { error: err } = await supabase
        .from('pets')
        .update({
          happiness: pet.value.happiness,
          hunger: pet.value.hunger,
          energy: pet.value.energy,
          cleanliness: pet.value.cleanliness,
          health: pet.value.health,
          last_interaction: pet.value.last_interaction
        })
        .eq('id', pet.value.id);
      if (err) throw err;
      lastSave.value = new Date();
    } catch (err) {
      console.error('Error saving pet state:', err);
    }
  }

  async function loadMessages() {
    if (!pet.value) {
      messages.value = [];
      return;
    }
    try {
      console.log('Cargando mensajes para mascota:', pet.value.id);
      const { data, error: err } = await supabase
        .from('messages')
        .select('*')
        .eq('pet_id', pet.value.id)
        .order('timestamp', { ascending: false })
        .limit(20);
      if (err) throw err;
      messages.value = data || [];
      console.log('Mensajes cargados:', messages.value.length);
    } catch (err) {
      console.error('Error loading messages:', err);
      messages.value = [];
    }
  }

  async function sendMessage(content) {
    if (!pet.value) return;
    try {
      const timestamp = new Date().toISOString();
      const userMessage = {
        id: `temp-${Date.now()}`,
        content,
        sender: 'user',
        timestamp,
        pet_id: pet.value.id
      };
      messages.value = [userMessage, ...messages.value];
      const { data: userData, error: userErr } = await supabase
        .from('messages')
        .insert({
          content,
          sender: 'user',
          timestamp,
          pet_id: pet.value.id
        })
        .select()
        .single();
      if (userErr) throw userErr;
      messages.value = messages.value.map(m => m.id === userMessage.id ? userData : m);
      const petResponse = generatePetResponse(content);
      const petMessage = {
        id: `temp-${Date.now() + 1}`,
        content: petResponse,
        sender: 'pet',
        timestamp: new Date().toISOString(),
        pet_id: pet.value.id
      };
      messages.value = [petMessage, ...messages.value];
      const { data: petData, error: petErr } = await supabase
        .from('messages')
        .insert({
          content: petResponse,
          sender: 'pet',
          timestamp: petMessage.timestamp,
          pet_id: pet.value.id
        })
        .select()
        .single();
      if (petErr) throw petErr;
      messages.value = messages.value.map(m => m.id === petMessage.id ? petData : m);
      updateLastInteraction();
      await logInteraction('talk', 'Chat with pet');
    } catch (err) {
      console.error('Error sending message:', err);
    }
  }

  function generatePetResponse(userMessage) {
    if (!pet.value) return '...';
    const lowerMsg = userMessage.toLowerCase();
    if (lowerMsg.includes('hola') || lowerMsg.includes('hi') || lowerMsg.includes('hello')) {
      if (mood.value === 'happy') return `*mueve la cola* ¡Hola humano de ${pet.value.name}!`;
      if (mood.value === 'hungry') return `*te mira con tristeza* Hola... ¿tienes comida?`;
      if (mood.value === 'sleepy') return `*bosteza* Hola... estoy algo cansado...`;
      if (mood.value === 'dirty') return `*estornuda* ¡Hola! Necesito un baño...`;
      if (mood.value === 'sad') return `*suspira* No me siento muy bien hoy...`;
      return `¡Hola! ¡Qué bueno verte!`;
    }
    if (lowerMsg.includes('comida') || lowerMsg.includes('comer') || lowerMsg.includes('hambre')) {
      if (pet.value.hunger > 70) return `¡Tengo mucha hambre! ¡Gracias por acordarte!`;
      if (pet.value.hunger < 30) return `En realidad estoy bastante lleno ahora mismo.`;
      return `Podría comer algo, ¡claro!`;
    }
    if (lowerMsg.includes('jugar') || lowerMsg.includes('juego') || lowerMsg.includes('diversión')) {
      if (pet.value.happiness > 70) return `*salta emocionado* ¡Sí! ¡Juguemos!`;
      if (pet.value.happiness < 30) return `*suspira* No tengo muchas ganas ahora...`;
      if (pet.value.energy < 30) return `Estoy demasiado cansado para jugar ahora...`;
      return `¡Claro, suena divertido!`;
    }
    if (lowerMsg.includes('dormir') || lowerMsg.includes('descansar') || lowerMsg.includes('cansado')) {
      if (pet.value.energy < 30) return `*bosteza* Sí, realmente necesito descansar...`;
      if (pet.value.energy > 70) return `¡Tengo demasiada energía para dormir ahora!`;
      return `Un poco de descanso no estaría mal.`;
    }
    switch (mood.value) {
      case 'happy':
        return `*te mira con ojos felices* ¡Estoy teniendo un gran día!`;
      case 'hungry':
        return `*gruñe el estómago* Tengo bastante hambre...`;
      case 'sleepy':
        return `*bosteza* Estoy muy cansado...`;
      case 'dirty':
        return `*se rasca* Necesito un poco de limpieza...`;
      case 'sad':
        return `*suspira* No me siento muy bien hoy...`;
      default:
        return `*inclina la cabeza* ¿En qué estás pensando?`;
    }
  }

  async function feedPet() {
    if (!pet.value) return;
    try {
      const updatedHunger = Math.max(0, Math.min(100, pet.value.hunger - 20));
      const updatedEnergy = Math.max(0, Math.min(100, pet.value.energy - 5));
      const updatedHappiness = Math.max(0, Math.min(100, pet.value.happiness + 5));
      const updates = {
        hunger: updatedHunger,
        energy: updatedEnergy,
        happiness: updatedHappiness,
        last_interaction: new Date().toISOString()
      };
      pet.value = { ...pet.value, ...updates };
      const { error: err } = await supabase
        .from('pets')
        .update(updates)
        .eq('id', pet.value.id);
      if (err) throw err;
      addSystemMessage(`*${pet.value.name} come felizmente*`);
      await logInteraction('feed', 'Pet fed');
    } catch (err) {
      console.error('Error feeding pet:', err);
    }
  }

  async function playWithPet() {
    if (!pet.value) return;
    try {
      const updatedHappiness = Math.max(0, Math.min(100, pet.value.happiness + 15));
      const updatedEnergy = Math.max(0, Math.min(100, pet.value.energy - 10));
      const updatedHunger = Math.max(0, Math.min(100, pet.value.hunger + 10));
      const updates = {
        happiness: updatedHappiness,
        energy: updatedEnergy,
        hunger: updatedHunger,
        last_interaction: new Date().toISOString()
      };
      pet.value = { ...pet.value, ...updates };
      const { error: err } = await supabase
        .from('pets')
        .update(updates)
        .eq('id', pet.value.id);
      if (err) throw err;
      addSystemMessage(`*${pet.value.name} juega entusiasmadamente*`);
      await logInteraction('play', 'Played with pet');
    } catch (err) {
      console.error('Error playing with pet:', err);
    }
  }

  async function restPet() {
    if (!pet.value) return;
    try {
      const updatedEnergy = Math.max(0, Math.min(100, pet.value.energy + 30));
      const updatedHunger = Math.max(0, Math.min(100, pet.value.hunger + 5));
      const updates = {
        energy: updatedEnergy,
        hunger: updatedHunger,
        last_interaction: new Date().toISOString()
      };
      pet.value = { ...pet.value, ...updates };
      const { error: err } = await supabase
        .from('pets')
        .update(updates)
        .eq('id', pet.value.id);
      if (err) throw err;
      addSystemMessage(`*${pet.value.name} descansa plácidamente*`);
      await logInteraction('rest', 'Pet rested');
    } catch (err) {
      console.error('Error resting pet:', err);
    }
  }

  async function cleanPet() {
    if (!pet.value) return;
    try {
      const updatedCleanliness = Math.max(0, Math.min(100, pet.value.cleanliness + 30));
      const updatedHappiness = Math.max(0, Math.min(100, pet.value.happiness + 5));
      const updates = {
        cleanliness: updatedCleanliness,
        happiness: updatedHappiness,
        last_interaction: new Date().toISOString()
      };
      pet.value = { ...pet.value, ...updates };
      const { error: err } = await supabase
        .from('pets')
        .update(updates)
        .eq('id', pet.value.id);
      if (err) throw err;
      addSystemMessage(`*${pet.value.name} está ahora limpio y fresco*`);
      await logInteraction('clean', 'Pet cleaned');
    } catch (err) {
      console.error('Error cleaning pet:', err);
    }
  }

  function addSystemMessage(content) {
    if (!pet.value) return;
    const systemMessage = {
      id: `system-${Date.now()}`,
      content,
      sender: 'pet',
      timestamp: new Date().toISOString(),
      pet_id: pet.value.id
    };
    messages.value = [systemMessage, ...messages.value];
  }

  async function logInteraction(type, description) {
    if (!pet.value) return;
    try {
      await supabase
        .from('interactions')
        .insert({
          pet_id: pet.value.id,
          type,
          description,
          timestamp: new Date().toISOString()
        });
    } catch (err) {
      console.error('Error logging interaction:', err);
    }
  }

  function updateLastInteraction() {
    if (!pet.value) return;
    const timestamp = new Date().toISOString();
    pet.value.last_interaction = timestamp;
  }

  async function deletePet(petId) {
    if (!petId) {
      console.error('Error: petId is undefined or null');
      error.value = 'ID de mascota no válido';
      return false;
    }

    console.log('deletePet llamado con ID:', petId);

    try {
      loading.value = true;
      error.value = null;

      // Verificar que la mascota pertenece al usuario actual
      const { data: petData, error: petCheckError } = await supabase
        .from('pets')
        .select('id, name, owner_id')
        .eq('id', petId)
        .single();

      if (petCheckError) {
        console.error('Error al verificar mascota:', petCheckError);
        throw new Error('No se pudo verificar la mascota');
      }

      if (!petData) {
        throw new Error('Mascota no encontrada');
      }

      console.log('Mascota encontrada:', petData);

      // Usar borrado manual en cascada directamente (más confiable)
      console.log('Iniciando borrado manual en cascada para pet ID:', petId);

      // Primero verificar qué datos existen
      console.log('Verificando datos existentes...');

      const { data: existingInteractions, error: checkIntError } = await supabase
        .from('interactions')
        .select('id')
        .eq('pet_id', petId);

      console.log('Interacciones encontradas:', existingInteractions?.length || 0);

      const { data: existingMessages, error: checkMsgError } = await supabase
        .from('messages')
        .select('id')
        .eq('pet_id', petId);

      console.log('Mensajes encontrados:', existingMessages?.length || 0);

      // Paso 1: Borrar interacciones
      console.log('Borrando interacciones...');
      const { data: deletedInteractions, error: interactionsError } = await supabase
        .from('interactions')
        .delete()
        .eq('pet_id', petId)
        .select();

      if (interactionsError) {
        console.error('Error al borrar interacciones:', interactionsError);
        console.log('Detalles del error de interacciones:', {
          code: interactionsError.code,
          message: interactionsError.message,
          details: interactionsError.details
        });
        // No lanzar error, continuar con el borrado
      } else {
        console.log('Interacciones borradas exitosamente:', deletedInteractions?.length || 0);
      }

      // Paso 2: Borrar mensajes
      console.log('Borrando mensajes...');
      const { data: deletedMessages, error: messagesError } = await supabase
        .from('messages')
        .delete()
        .eq('pet_id', petId)
        .select();

      if (messagesError) {
        console.error('Error al borrar mensajes:', messagesError);
        console.log('Detalles del error de mensajes:', {
          code: messagesError.code,
          message: messagesError.message,
          details: messagesError.details
        });
        // No lanzar error, continuar con el borrado
      } else {
        console.log('Mensajes borrados exitosamente:', deletedMessages?.length || 0);
      }

      // Verificar que no queden datos relacionados
      console.log('Verificando que no queden datos relacionados...');

      const { data: remainingInteractions } = await supabase
        .from('interactions')
        .select('id')
        .eq('pet_id', petId);

      const { data: remainingMessages } = await supabase
        .from('messages')
        .select('id')
        .eq('pet_id', petId);

      console.log('Interacciones restantes:', remainingInteractions?.length || 0);
      console.log('Mensajes restantes:', remainingMessages?.length || 0);

      // Paso 3: Borrar la mascota
      console.log('Borrando mascota...');
      const { data: deletedPet, error: petError } = await supabase
        .from('pets')
        .delete()
        .eq('id', petId)
        .select();

      if (petError) {
        console.error('Error al borrar mascota:', petError);
        console.log('Detalles del error de mascota:', {
          code: petError.code,
          message: petError.message,
          details: petError.details,
          hint: petError.hint
        });
        throw petError;
      }

      console.log('Mascota borrada exitosamente:', deletedPet);

      // Actualizar la lista local
      pets.value = pets.value.filter(p => p.id !== petId);

      // Si la mascota actual es la que se está borrando, limpiar el estado
      if (pet.value && pet.value.id === petId) {
        pet.value = null;
        messages.value = [];
      }

      return true;
    } catch (err) {
      console.error('Error en deletePet:', err);
      error.value = err.message || 'Error desconocido al borrar la mascota';

      // Si falla el borrado manual, intentar con función RPC como último recurso
      console.log('Intentando borrado con función RPC como último recurso...');
      try {
        // Intentar con la función principal
        let { data: rpcData, error: rpcError } = await supabase
          .rpc('delete_pet_with_related_data', { target_pet_id: petId });

        if (rpcError) {
          console.log('Intentando con función wrapper...');
          const result = await supabase
            .rpc('delete_pet_with_related_data_wrapper', { pet_id_param: petId });
          rpcData = result.data;
          rpcError = result.error;
        }

        if (!rpcError) {
          console.log('Borrado con RPC exitoso como último recurso');
          // Actualizar la lista local
          pets.value = pets.value.filter(p => p.id !== petId);
          if (pet.value && pet.value.id === petId) {
            pet.value = null;
            messages.value = [];
          }
          return true;
        } else {
          console.error('Error en RPC como último recurso:', rpcError);
        }
      } catch (rpcErr) {
        console.error('Error al intentar RPC como último recurso:', rpcErr);
      }

      return false;
    } finally {
      loading.value = false;
    }
  }

  // Función alternativa que usa solo la función RPC
  async function deletePetWithRPC(petId) {
    if (!petId) {
      console.error('Error: petId is undefined or null');
      error.value = 'ID de mascota no válido';
      return false;
    }

    console.log('deletePetWithRPC llamado con ID:', petId);

    try {
      loading.value = true;
      error.value = null;

      // Intentar con diferentes versiones de la función
      console.log('Intentando RPC con target_pet_id...');
      let { data, error: rpcError } = await supabase
        .rpc('delete_pet_with_related_data', { target_pet_id: petId });

      if (rpcError) {
        console.log('Intentando RPC con función wrapper...');
        const result = await supabase
          .rpc('delete_pet_with_related_data_wrapper', { pet_id_param: petId });
        data = result.data;
        rpcError = result.error;
      }

      if (rpcError) {
        console.log('Intentando RPC con pet_id (función antigua)...');
        const result = await supabase
          .rpc('delete_pet_with_related_data', { pet_id: petId });
        data = result.data;
        rpcError = result.error;
      }

      if (rpcError) {
        console.error('Error en RPC:', rpcError);
        throw rpcError;
      }

      console.log('Borrado con RPC exitoso:', data);

      // Actualizar la lista local
      pets.value = pets.value.filter(p => p.id !== petId);

      // Si la mascota actual es la que se está borrando, limpiar el estado
      if (pet.value && pet.value.id === petId) {
        pet.value = null;
        messages.value = [];
      }

      return true;
    } catch (err) {
      console.error('Error en deletePetWithRPC:', err);
      error.value = err.message || 'Error desconocido al borrar la mascota';
      return false;
    } finally {
      loading.value = false;
    }
  }

  async function deletePetAlternative(petId) {
    console.log('Usando método alternativo para borrar mascota con ID:', petId);
    
    try {
      // Obtener el token de autenticación
      const { data: authData } = await supabase.auth.getSession();
      if (!authData || !authData.session) {
        throw new Error('No hay sesión activa');
      }
      
      const token = authData.session.access_token;
      const apiUrl = `${supabase.supabaseUrl}/rest/v1/pets?id=eq.${petId}`;
      
      console.log('Enviando solicitud DELETE a:', apiUrl);
      
      // Usar fetch para hacer la solicitud DELETE directamente
      const response = await fetch(apiUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'apikey': supabase.supabaseKey
        }
      });
      
      console.log('Respuesta de la API:', response.status, response.statusText);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Error al borrar: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      console.log('Mascota borrada correctamente usando método alternativo');
      
      // Actualizar el estado local
      if (pet.value && pet.value.id === petId) {
        pet.value = null;
      }
      
      // Actualizar la lista de mascotas
      pets.value = pets.value.filter(p => p.id !== petId);
      
      return true;
    } catch (err) {
      console.error('Error en deletePetAlternative:', err);
      error.value = err.message;
      return false;
    }
  }

  async function updatePetName(petId, newName) {
    try {
      loading.value = true;
      error.value = null;
      const { error: err } = await supabase.from('pets').update({ name: newName }).eq('id', petId);
      if (err) throw err;
      
      // Si la mascota editada es la actual, actualiza el nombre localmente
      if (pet.value && pet.value.id === petId) {
        pet.value.name = newName;
      }
      
      // Actualiza el nombre en la lista de mascotas
      const petIndex = pets.value.findIndex(p => p.id === petId);
      if (petIndex !== -1) {
        // Crear un nuevo array para forzar la reactividad
        const updatedPets = [...pets.value];
        updatedPets[petIndex] = { ...updatedPets[petIndex], name: newName };
        pets.value = updatedPets;
      }
      
      return true;
    } catch (err) {
      error.value = err.message;
      return false;
    } finally {
      loading.value = false;
    }
  }

  async function softDeletePet(petId) {
    console.log('Realizando borrado lógico de mascota con ID:', petId);
    
    if (!petId) {
      console.error('Error: petId is undefined or null');
      error.value = 'ID de mascota no válido';
      return false;
    }
    
    try {
      loading.value = true;
      error.value = null;
      
      // Actualizar la mascota para marcarla como borrada
      const { data, error: updateError } = await supabase
        .from('pets')
        .update({ 
          deleted: true, 
          deleted_at: new Date().toISOString() 
        })
        .eq('id', petId);
      
      if (updateError) {
        console.error('Error al marcar mascota como borrada:', updateError);
        throw updateError;
      }
      
      console.log('Mascota marcada como borrada:', data);
      
      // Actualizar el estado local
      if (pet.value && pet.value.id === petId) {
        pet.value = null;
        messages.value = [];
      }
      
      // Actualizar la lista de mascotas
      pets.value = pets.value.filter(p => p.id !== petId);
      
      return true;
    } catch (err) {
      console.error('Error en softDeletePet:', err);
      error.value = err.message;
      return false;
    } finally {
      loading.value = false;
    }
  }

  async function deletePetWithRPC(petId) {
    console.log('Usando RPC para borrar mascota con ID:', petId);
    
    if (!petId) {
      console.error('Error: petId is undefined or null');
      error.value = 'ID de mascota no válido';
      return false;
    }
    
    try {
      loading.value = true;
      error.value = null;
      
      // Llamar a una función RPC en Supabase que maneje todo el proceso de borrado
      const { data, error: rpcError } = await supabase
        .rpc('delete_pet_with_related_data', { pet_id: petId });
      
      if (rpcError) {
        console.error('Error en RPC delete_pet_with_related_data:', rpcError);
        throw rpcError;
      }
      
      console.log('Resultado de RPC:', data);
      
      // Actualizar el estado local
      if (pet.value && pet.value.id === petId) {
        pet.value = null;
        messages.value = [];
      }
      
      // Actualizar la lista de mascotas
      pets.value = pets.value.filter(p => p.id !== petId);
      
      return true;
    } catch (err) {
      console.error('Error in deletePetWithRPC:', err);
      error.value = err.message;
      return false;
    } finally {
      loading.value = false;
    }
  }

  function cleanup() {
    stopAutoSave();
  }

  return {
    pet,
    pets, // Asegurarse de que pets se exporta directamente
    loading,
    error,
    lastSave,
    mood,
    createPet,
    getPetByOwnerId,
    fetchPets,
    setCurrentPet,
    deletePet,
    deletePetAlternative,
    updatePetName,
    savePetState,
    loadMessages,
    sendMessage,
    feedPet,
    playWithPet,
    restPet,
    cleanPet,
    cleanup,
    PetType,
    softDeletePet,
    deletePetWithRPC
  };
});
