import { defineStore } from 'pinia'
import { ref } from 'vue'
import { supabase } from '../services/supabase'
import type { User } from '../types'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  async function initialize() {
    const { data } = await supabase.auth.getSession()
    
    if (data.session) {
      user.value = {
        id: data.session.user.id,
        email: data.session.user.email || '',
      }
    }
  }

  async function login(email: string, password: string) {
    try {
      loading.value = true
      error.value = null
      
      const { data, error: err } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      if (err) throw err
      
      if (data.user) {
        user.value = {
          id: data.user.id,
          email: data.user.email || '',
        }
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  async function register(email: string, password: string, username: string) {
    try {
      loading.value = true
      error.value = null
      
      const { data, error: err } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username
          }
        }
      })
      
      if (err) throw err
      
      if (data.user) {
        user.value = {
          id: data.user.id,
          email: data.user.email || '',
          username
        }
      }
    } catch (err: any) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  async function logout() {
    try {
      await supabase.auth.signOut()
      user.value = null
    } catch (err: any) {
      error.value = err.message
    }
  }

  return {
    user,
    loading,
    error,
    initialize,
    login,
    register,
    logout
  }
})