<script setup>
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/authStore.js';
import { usePetStore } from '../stores/petStore.js';
import { computed, onMounted } from 'vue';

const router = useRouter();
const authStore = useAuthStore();
const petStore = usePetStore();

const loading = computed(() => petStore.loading);
const hasPet = computed(() => petStore.pets && petStore.pets.length > 0);

onMounted(async () => {
  if (authStore.user) {
    await petStore.fetchPets();
  }
});

function goToCreatePet() {
  router.push('/create-pet');
}

function logout() {
  authStore.logout();
  router.push('/login');
}
</script>

<template>
  <div class="home-container">
    <div class="container">
      <header class="home-header">
        <h1>Mascota Virtual Interactiva</h1>
       
      </header>
      
      <main class="home-content">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Cargando...</p>
        </div>
        
        <div v-else-if="!hasPet" class="welcome-container">
          <div class="welcome-card">
            <h2>¡Bienvenido a Mascota Virtual!</h2>
            <p>
              Parece que aún no tienes una mascota virtual. 
              ¡Crea una ahora y comienza a disfrutar de la experiencia!
            </p>
            <div class="pet-types">
              <div class="pet-type">
                <img src="https://images.pexels.com/photos/45201/kitty-cat-kitten-pet-45201.jpeg" alt="Cat" />
                <span>Gato</span>
              </div>
              <div class="pet-type">
                <img src="https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg" alt="Dog" />
                <span>Perro</span>
              </div>
              <div class="pet-type">
                <img src="https://images.pexels.com/photos/326012/pexels-photo-326012.jpeg" alt="Bunny" />
                <span>Conejo</span>
              </div>
            </div>
            <button class="btn-primary create-pet-btn" @click="goToCreatePet">
              Crear mi mascota
            </button>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  min-height: 100vh;
  background-color: var(--neutral-200);
  padding: var(--spacing-md) 0;
}

.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-md) 0;
}

.home-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 70vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--neutral-300);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.welcome-container {
  width: 100%;
  max-width: 600px;
}

.welcome-card {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  text-align: center;
}

.pet-types {
  display: flex;
  justify-content: space-around;
  margin: var(--spacing-lg) 0;
}

.pet-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.pet-type img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 50%;
  border: 3px solid var(--primary-light);
  transition: transform var(--transition-normal);
}

.pet-type:hover img {
  transform: scale(1.05);
  border-color: var(--primary-color);
}

.create-pet-btn {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

@media (max-width: 768px) {
  .pet-types {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .pet-type img {
    width: 80px;
    height: 80px;
  }
}
</style>