<script setup>
import { useAuthStore } from '../stores/authStore.js';
import { usePetStore } from '../stores/petStore.js';
import { useRouter } from 'vue-router';
import { onMounted } from 'vue';

const authStore = useAuthStore();
const petStore = usePetStore();
const router = useRouter();
const user = authStore.user;
const pets = petStore.pets;

onMounted(async () => {
  if (user) {
    await petStore.fetchPets(user.id);
  }
});

async function goToPet(pet) {
  petStore.setCurrentPet(pet);
  router.push({ name: 'PetInteraction', params: { id: pet.id } });
}

async function deletePet(pet) {
  if (confirm(`¿Seguro que quieres borrar a ${pet.name}? Esta acción no se puede deshacer.`)) {
    try {
      console.log('Intentando borrar mascota desde perfil:', pet.name, 'ID:', pet.id);
      const success = await petStore.deletePet(pet.id);
      if (success) {
        // Recargar la lista de mascotas
        await petStore.fetchPets(authStore.user.id);
        alert(`${pet.name} ha sido eliminado correctamente.`);
      } else {
        console.error('Borrado falló, error del store:', petStore.error);

        // Ofrecer método alternativo
        if (confirm(`El borrado normal falló. ¿Quieres intentar con el método alternativo (RPC)?\n\nError: ${petStore.error}`)) {
          console.log('Intentando borrado con RPC desde perfil...');
          const rpcSuccess = await petStore.deletePetWithRPC(pet.id);
          if (rpcSuccess) {
            await petStore.fetchPets(authStore.user.id);
            alert(`${pet.name} ha sido eliminado correctamente usando método alternativo.`);
          } else {
            alert('Error al borrar la mascota con método alternativo: ' + (petStore.error || 'Error desconocido.'));
          }
        }
      }
    } catch (error) {
      console.error('Error al borrar mascota:', error);
      alert('Error al borrar la mascota: ' + error.message);
    }
  }
}
</script>

<template>
  <div class="profile-container">
    <div class="profile-card">
      <h1>Mi Perfil</h1>
      <div v-if="user">
        <div class="profile-row">
          <span class="profile-label">Email:</span>
          <span>{{ user.email }}</span>
        </div>
        <div class="profile-row">
          <span class="profile-label">Nombre de usuario:</span>
          <span>{{ user.username || 'Sin nombre de usuario' }}</span>
        </div>
        <div class="profile-row">
          <span class="profile-label">Mis mascotas:</span>
        </div>
        <ul v-if="pets.length > 0" class="pet-list">
          <li v-for="pet in pets" :key="pet.id" class="pet-item">
            <span class="pet-name" @click="goToPet(pet)" style="cursor:pointer;">{{ pet.name }}</span>
            <span class="pet-type">({{ pet.type }})</span>
            <button class="delete-pet-btn" :disabled="petStore.loading" @click.stop="deletePet(pet)">🗑️</button>
          </li>
        </ul>
        <div v-else class="no-pets">No tienes mascotas registradas.</div>
      </div>
      <div v-else>
        <p>No has iniciado sesión.</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.profile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  background: var(--neutral-200);
}
.profile-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-xl);
  min-width: 320px;
  max-width: 400px;
  width: 100%;
}
.profile-card h1 {
  color: var(--primary-dark);
  text-align: center;
  margin-bottom: var(--spacing-xl);
}
.profile-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-md);
}
.profile-label {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-700);
}
.pet-list {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--spacing-lg) 0;
}
.pet-item {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  padding: var(--spacing-xs) 0;
  font-size: var(--font-size-md);
}
.pet-name {
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}
.pet-type {
  color: var(--neutral-600);
  font-size: var(--font-size-sm);
}
.no-pets {
  color: var(--neutral-500);
  font-style: italic;
  margin-bottom: var(--spacing-lg);
}
.delete-pet-btn {
  background: none;
  border: none;
  color: var(--error-color);
  font-size: 1.2em;
  cursor: pointer;
  margin-left: 0.5em;
  transition: color 0.2s;
}
.delete-pet-btn:hover {
  color: #b71c1c;
}
</style>
