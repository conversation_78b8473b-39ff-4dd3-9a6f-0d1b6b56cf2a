<script setup>
import { useAuthStore } from '../stores/authStore.js';
import { usePetStore } from '../stores/petStore.js';
import { useRouter } from 'vue-router';
import { onMounted, ref } from 'vue';

const authStore = useAuthStore();
const petStore = usePetStore();
const router = useRouter();
const user = authStore.user;
const pets = petStore.pets;

// Variables para los modales
const showConfirmDialog = ref(false);
const showSuccessDialog = ref(false);
const showErrorDialog = ref(false);
const showRetryDialog = ref(false);

const confirmMessage = ref('');
const successMessage = ref('');
const errorMessage = ref('');
const retryMessage = ref('');

const petToDelete = ref(null);
const pendingAction = ref(null);

onMounted(async () => {
  if (user) {
    await petStore.fetchPets(user.id);
  }
});

async function goToPet(pet) {
  petStore.setCurrentPet(pet);
  router.push({ name: 'PetInteraction', params: { id: pet.id } });
}

// Funciones para manejar los modales
function showConfirmModal(message, pet) {
  confirmMessage.value = message;
  petToDelete.value = pet;
  showConfirmDialog.value = true;
}

function closeConfirmDialog() {
  showConfirmDialog.value = false;
  petToDelete.value = null;
}

function showSuccessModal(message) {
  successMessage.value = message;
  showSuccessDialog.value = true;
}

function closeSuccessDialog() {
  showSuccessDialog.value = false;
}

function showErrorModal(message) {
  errorMessage.value = message;
  showErrorDialog.value = true;
}

function closeErrorDialog() {
  showErrorDialog.value = false;
}

function showRetryModal(message, action) {
  retryMessage.value = message;
  pendingAction.value = action;
  showRetryDialog.value = true;
}

function closeRetryDialog() {
  showRetryDialog.value = false;
  pendingAction.value = null;
}

async function confirmRetry(retry) {
  closeRetryDialog();
  if (retry && pendingAction.value) {
    await pendingAction.value();
  }
}

function deletePet(pet) {
  showConfirmModal(
    `¿Seguro que quieres borrar a ${pet.name}? Esta acción no se puede deshacer.`,
    pet
  );
}

// Función que se ejecuta cuando se confirma el borrado
async function confirmDelete(confirmed) {
  closeConfirmDialog();

  if (!confirmed || !petToDelete.value) return;

  const pet = petToDelete.value;

  try {
    console.log('Intentando borrar mascota desde perfil:', pet.name, 'ID:', pet.id);
    const success = await petStore.deletePet(pet.id);

    if (success) {
      // Recargar la lista de mascotas
      await petStore.fetchPets(authStore.user.id);
      showSuccessModal(`${pet.name} ha sido eliminado correctamente.`);
    } else {
      console.error('Borrado falló, error del store:', petStore.error);

      // Ofrecer método alternativo usando modal
      const retryAction = async () => {
        try {
          console.log('Intentando borrado con RPC desde perfil...');
          const rpcSuccess = await petStore.deletePetWithRPC(pet.id);
          if (rpcSuccess) {
            await petStore.fetchPets(authStore.user.id);
            showSuccessModal(`${pet.name} ha sido eliminado correctamente usando método alternativo.`);
          } else {
            showErrorModal('Error al borrar la mascota con método alternativo: ' + (petStore.error || 'Error desconocido.'));
          }
        } catch (error) {
          console.error('Error en método alternativo:', error);
          showErrorModal('Error al borrar la mascota con método alternativo: ' + error.message);
        }
      };

      showRetryModal(
        `El borrado normal falló: ${petStore.error || 'Error desconocido'}`,
        retryAction
      );
    }
  } catch (error) {
    console.error('Error al borrar mascota:', error);
    showErrorModal('Error al borrar la mascota: ' + error.message);
  }
}
</script>

<template>
  <div class="profile-container">
    <div class="profile-card">
      <h1>Mi Perfil</h1>
      <div v-if="user">
        <div class="profile-row">
          <span class="profile-label">Email:</span>
          <span>{{ user.email }}</span>
        </div>
        <div class="profile-row">
          <span class="profile-label">Nombre de usuario:</span>
          <span>{{ user.username || 'Sin nombre de usuario' }}</span>
        </div>
        <div class="profile-row">
          <span class="profile-label">Mis mascotas:</span>
        </div>
        <ul v-if="pets.length > 0" class="pet-list">
          <li v-for="pet in pets" :key="pet.id" class="pet-item">
            <span class="pet-name" @click="goToPet(pet)" style="cursor:pointer;">{{ pet.name }}</span>
            <span class="pet-type">({{ pet.type }})</span>
            <button class="delete-pet-btn" :disabled="petStore.loading" @click.stop="deletePet(pet)">🗑️</button>
          </li>
        </ul>
        <div v-else class="no-pets">No tienes mascotas registradas.</div>
      </div>
      <div v-else>
        <p>No has iniciado sesión.</p>
      </div>
    </div>

    <!-- Modal de confirmación de borrado -->
    <div v-if="showConfirmDialog" class="modal-overlay" @click="closeConfirmDialog">
      <div class="modal-dialog confirm-modal" @click.stop>
        <div class="modal-header">
          <h3>⚠️ Confirmar eliminación</h3>
        </div>
        <div class="modal-body">
          <p>{{ confirmMessage }}</p>
        </div>
        <div class="modal-footer">
          <button @click="confirmDelete(false)" class="btn btn-secondary">Cancelar</button>
          <button @click="confirmDelete(true)" class="btn btn-danger">Sí, eliminar</button>
        </div>
      </div>
    </div>

    <!-- Modal de éxito -->
    <div v-if="showSuccessDialog" class="modal-overlay" @click="closeSuccessDialog">
      <div class="modal-dialog success-modal" @click.stop>
        <div class="modal-header">
          <h3>✅ Operación exitosa</h3>
        </div>
        <div class="modal-body">
          <p>{{ successMessage }}</p>
        </div>
        <div class="modal-footer">
          <button @click="closeSuccessDialog" class="btn btn-primary">Aceptar</button>
        </div>
      </div>
    </div>

    <!-- Modal de error -->
    <div v-if="showErrorDialog" class="modal-overlay" @click="closeErrorDialog">
      <div class="modal-dialog error-modal" @click.stop>
        <div class="modal-header">
          <h3>❌ Error</h3>
        </div>
        <div class="modal-body">
          <p>{{ errorMessage }}</p>
        </div>
        <div class="modal-footer">
          <button @click="closeErrorDialog" class="btn btn-secondary">Cerrar</button>
        </div>
      </div>
    </div>

    <!-- Modal de reintentar -->
    <div v-if="showRetryDialog" class="modal-overlay" @click="closeRetryDialog">
      <div class="modal-dialog retry-modal" @click.stop>
        <div class="modal-header">
          <h3>🔄 Método alternativo</h3>
        </div>
        <div class="modal-body">
          <p>{{ retryMessage }}</p>
          <p><small>¿Quieres intentar con el método alternativo (RPC)?</small></p>
        </div>
        <div class="modal-footer">
          <button @click="confirmRetry(false)" class="btn btn-secondary">No, cancelar</button>
          <button @click="confirmRetry(true)" class="btn btn-warning">Sí, intentar</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.profile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  background: var(--neutral-200);
}
.profile-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-xl);
  min-width: 320px;
  max-width: 400px;
  width: 100%;
}
.profile-card h1 {
  color: var(--primary-dark);
  text-align: center;
  margin-bottom: var(--spacing-xl);
}
.profile-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-md);
}
.profile-label {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-700);
}
.pet-list {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--spacing-lg) 0;
}
.pet-item {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  padding: var(--spacing-xs) 0;
  font-size: var(--font-size-md);
}
.pet-name {
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}
.pet-type {
  color: var(--neutral-600);
  font-size: var(--font-size-sm);
}
.no-pets {
  color: var(--neutral-500);
  font-style: italic;
  margin-bottom: var(--spacing-lg);
}
.delete-pet-btn {
  background: none;
  border: none;
  color: var(--error-color);
  font-size: 1.2em;
  cursor: pointer;
  margin-left: 0.5em;
  transition: color 0.2s;
}
.delete-pet-btn:hover {
  color: #b71c1c;
}

/* Estilos para los modales */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.modal-dialog {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 450px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

.modal-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-body {
  padding: 20px 24px;
  line-height: 1.6;
}

.modal-body p {
  margin: 0 0 12px 0;
}

.modal-body small {
  color: #6c757d;
  font-style: italic;
}

.modal-footer {
  padding: 16px 24px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

/* Botones de los modales */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background-color: #42b983;
  color: white;
}

.btn-primary:hover {
  background-color: #369870;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-danger {
  background-color: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background-color: #c0392b;
}

.btn-warning {
  background-color: #f39c12;
  color: white;
}

.btn-warning:hover {
  background-color: #e67e22;
}

/* Estilos específicos por tipo de modal */
.confirm-modal .modal-header {
  background-color: #fff3cd;
  border-bottom-color: #ffeaa7;
}

.success-modal .modal-header {
  background-color: #d4edda;
  border-bottom-color: #c3e6cb;
}

.error-modal .modal-header {
  background-color: #f8d7da;
  border-bottom-color: #f5c6cb;
}

.retry-modal .modal-header {
  background-color: #e2e3e5;
  border-bottom-color: #d6d8db;
}

/* Animaciones */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive */
@media (max-width: 480px) {
  .modal-dialog {
    width: 95%;
    margin: 20px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}
</style>
