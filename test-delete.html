<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Delete Function</title>
</head>
<body>
    <h1>Test de Función de Borrado</h1>
    <div id="output"></div>
    
    <script type="module">
        import { supabase } from './src/services/supabase.js';
        
        const output = document.getElementById('output');
        
        function log(message) {
            console.log(message);
            output.innerHTML += '<p>' + JSON.stringify(message) + '</p>';
        }
        
        async function testDeleteFunction() {
            log('Iniciando test de función de borrado...');
            
            try {
                // Probar la función RPC con el nuevo parámetro
                log('Probando función RPC con pet_id_param...');
                const { data, error } = await supabase
                    .rpc('delete_pet_with_related_data', { pet_id_param: '00000000-0000-0000-0000-000000000000' });
                
                if (error) {
                    log('Error en RPC: ' + error.message);
                    
                    // Probar con el parámetro antiguo
                    log('Probando función RPC con pet_id...');
                    const { data: data2, error: error2 } = await supabase
                        .rpc('delete_pet_with_related_data', { pet_id: '00000000-0000-0000-0000-000000000000' });
                    
                    if (error2) {
                        log('Error en RPC con parámetro antiguo: ' + error2.message);
                    } else {
                        log('RPC funciona con parámetro antiguo: ' + data2);
                    }
                } else {
                    log('RPC funciona con nuevo parámetro: ' + data);
                }
                
                // Probar borrado manual en cascada
                log('Probando borrado manual...');
                
                // Probar borrado de interacciones
                const { error: intError } = await supabase
                    .from('interactions')
                    .delete()
                    .eq('pet_id', '00000000-0000-0000-0000-000000000000');
                
                if (intError) {
                    log('Error al borrar interacciones: ' + intError.message);
                } else {
                    log('Borrado de interacciones OK');
                }
                
                // Probar borrado de mensajes
                const { error: msgError } = await supabase
                    .from('messages')
                    .delete()
                    .eq('pet_id', '00000000-0000-0000-0000-000000000000');
                
                if (msgError) {
                    log('Error al borrar mensajes: ' + msgError.message);
                } else {
                    log('Borrado de mensajes OK');
                }
                
                // Probar borrado de mascota
                const { error: petError } = await supabase
                    .from('pets')
                    .delete()
                    .eq('id', '00000000-0000-0000-0000-000000000000');
                
                if (petError) {
                    log('Error al borrar mascota: ' + petError.message);
                } else {
                    log('Borrado de mascota OK');
                }
                
            } catch (error) {
                log('Error general: ' + error.message);
            }
        }
        
        // Ejecutar test al cargar la página
        testDeleteFunction();
    </script>
</body>
</html>
