<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Pets Loading</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        button { padding: 10px 20px; margin: 10px 0; background: #42b983; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #369870; }
        .pet-item { background: #f0f0f0; padding: 10px; margin: 5px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Debug: Carga de Mascotas</h1>
    <p>Este script diagnostica por qué MisMascotas no está cargando las mascotas desde Supabase.</p>
    
    <button onclick="checkAuth()">1. Verificar Autenticación</button>
    <button onclick="testDirectQuery()">2. Consulta Directa a Supabase</button>
    <button onclick="testStoreFunction()">3. Probar Función del Store</button>
    <button onclick="clearLogs()">Limpiar Logs</button>
    
    <div id="logs"></div>
    <div id="pets-display"></div>
    
    <script type="module">
        import { supabase } from './src/services/supabase.js';
        
        const logsDiv = document.getElementById('logs');
        const petsDisplay = document.getElementById('pets-display');
        
        function addLog(message, type = 'log') {
            const logElement = document.createElement('div');
            logElement.className = `log ${type}`;
            logElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logElement);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }
        
        function displayPets(pets, title) {
            const petsHtml = pets.map(pet => `
                <div class="pet-item">
                    <strong>${pet.name}</strong> (${pet.type}) - ID: ${pet.id}<br>
                    Owner: ${pet.owner_id}<br>
                    Creado: ${new Date(pet.created_at).toLocaleString()}
                </div>
            `).join('');
            
            petsDisplay.innerHTML = `<h3>${title}</h3>${petsHtml}`;
        }
        
        window.clearLogs = function() {
            logsDiv.innerHTML = '';
            petsDisplay.innerHTML = '';
        }
        
        window.checkAuth = async function() {
            addLog('🔍 Verificando autenticación...', 'log');
            
            try {
                const { data: session, error } = await supabase.auth.getSession();
                
                if (error) {
                    addLog(`❌ Error al obtener sesión: ${error.message}`, 'error');
                    return;
                }
                
                if (!session || !session.session) {
                    addLog('❌ No hay sesión activa', 'error');
                    addLog('💡 Necesitas iniciar sesión en la aplicación principal primero', 'warning');
                    return;
                }
                
                const user = session.session.user;
                addLog(`✅ Usuario autenticado: ${user.email}`, 'success');
                addLog(`📋 ID de usuario: ${user.id}`, 'log');
                
                // Guardar el ID para usar en otras pruebas
                window.currentUserId = user.id;
                
            } catch (error) {
                addLog(`💥 Error general: ${error.message}`, 'error');
            }
        }
        
        window.testDirectQuery = async function() {
            if (!window.currentUserId) {
                addLog('⚠️ Primero verifica la autenticación', 'warning');
                return;
            }
            
            addLog('🔍 Realizando consulta directa a Supabase...', 'log');
            
            try {
                const { data, error } = await supabase
                    .from('pets')
                    .select('*')
                    .eq('owner_id', window.currentUserId)
                    .order('created_at', { ascending: true });
                
                if (error) {
                    addLog(`❌ Error en consulta: ${error.message}`, 'error');
                    addLog(`📋 Código de error: ${error.code}`, 'warning');
                    return;
                }
                
                addLog(`✅ Consulta exitosa: ${data.length} mascotas encontradas`, 'success');
                
                if (data.length === 0) {
                    addLog('📝 No hay mascotas para este usuario', 'warning');
                    addLog('💡 Verifica que hayas creado mascotas en la aplicación', 'log');
                } else {
                    addLog('📋 Mascotas encontradas:', 'log');
                    data.forEach((pet, index) => {
                        addLog(`  ${index + 1}. ${pet.name} (${pet.type}) - ID: ${pet.id}`, 'log');
                    });
                    
                    displayPets(data, 'Mascotas encontradas en Supabase');
                }
                
            } catch (error) {
                addLog(`💥 Error general: ${error.message}`, 'error');
            }
        }
        
        window.testStoreFunction = async function() {
            if (!window.currentUserId) {
                addLog('⚠️ Primero verifica la autenticación', 'warning');
                return;
            }
            
            addLog('🔍 Probando función fetchPets del store...', 'log');
            
            try {
                // Simular la función fetchPets
                const ownerId = window.currentUserId;
                addLog(`📋 Buscando mascotas para owner_id: ${ownerId}`, 'log');
                
                const query = supabase
                    .from('pets')
                    .select('*')
                    .eq('owner_id', ownerId)
                    .order('created_at', { ascending: true });
                
                const { data, error } = await query;
                
                if (error) {
                    addLog(`❌ Error en fetchPets simulado: ${error.message}`, 'error');
                    return;
                }
                
                addLog(`✅ fetchPets simulado exitoso: ${data.length} mascotas`, 'success');
                
                if (data.length > 0) {
                    displayPets(data, 'Resultado de fetchPets simulado');
                }
                
                // Verificar si hay problemas de reactividad
                addLog('🔍 Verificando posibles problemas...', 'log');
                
                if (data.length === 0) {
                    addLog('⚠️ Problema: No hay mascotas en la base de datos', 'warning');
                    addLog('💡 Solución: Crea una mascota en la aplicación principal', 'log');
                } else {
                    addLog('✅ Las mascotas existen en Supabase', 'success');
                    addLog('💡 El problema puede estar en la reactividad del componente Vue', 'warning');
                }
                
            } catch (error) {
                addLog(`💥 Error general: ${error.message}`, 'error');
            }
        }
        
        // Auto-ejecutar verificación de auth al cargar
        addLog('🚀 Herramienta de diagnóstico cargada', 'log');
        addLog('📝 Paso 1: Haz clic en "Verificar Autenticación"', 'log');
    </script>
</body>
</html>
