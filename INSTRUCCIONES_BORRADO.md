# Instrucciones para Corregir el Problema de Borrado

## Problema Identificado

Los botones de borrar no funcionan debido a dos problemas:

1. **Error en función RPC**: La columna `pet_id` es ambigua en la función SQL
2. **Restricciones de clave foránea**: No se pueden borrar mascotas que tienen datos relacionados

## Solución

### Paso 1: Actualizar la función SQL en Supabase

Ve a tu panel de Supabase → SQL Editor y ejecuta este código:

```sql
-- Función para borrar una mascota y todos sus datos relacionados
CREATE OR REPLACE FUNCTION delete_pet_with_related_data(pet_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Borrar interacciones relacionadas
  DELETE FROM interactions WHERE pet_id = pet_id_param;
  
  -- Borrar mensajes relacionados
  DELETE FROM messages WHERE pet_id = pet_id_param;
  
  -- Borrar la mascota
  DELETE FROM pets WHERE id = pet_id_param;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    -- En caso de error, devolver FALSE
    RETURN FALSE;
END;
$$;
```

### Paso 2: Verificar que la función funciona

Ejecuta este código en el SQL Editor para probar:

```sql
-- Probar la función (esto no borrará nada real porque el ID no existe)
SELECT delete_pet_with_related_data('00000000-0000-0000-0000-000000000000');
```

### Paso 3: Los cambios en el código ya están aplicados

He actualizado el código para:

1. **Usar el nuevo parámetro** `pet_id_param` en lugar de `pet_id`
2. **Implementar borrado manual en cascada** como fallback
3. **Mejorar el manejo de errores**

## Cambios Realizados en el Código

### En `src/stores/petStore.js`:
- Función `deletePet` mejorada con borrado en cascada manual
- Mejor logging para depuración
- Manejo robusto de errores

### En `src/views/MisMascotas.vue`:
- Simplificada para usar solo la función del store
- Eliminadas funciones redundantes
- Mejor feedback al usuario

### En `src/views/Profile.vue`:
- Corregida la llamada a `fetchPets` con ID de usuario
- Mejor manejo de errores

## Cómo Probar

1. **Ejecuta la función SQL** en Supabase (Paso 1)
2. **Recarga la aplicación** en el navegador
3. **Ve a "Mis Mascotas"** y prueba borrar una mascota
4. **Ve a "Perfil"** y prueba borrar desde ahí también

## Si Sigue Sin Funcionar

Abre la consola del navegador (F12) y verás logs detallados que te dirán exactamente qué está pasando:

- Si la función RPC funciona o falla
- Si el borrado manual en cascada funciona
- Cualquier error específico

Los logs te ayudarán a identificar si el problema está en:
- La función SQL no actualizada
- Permisos de la base de datos
- Otro problema específico
