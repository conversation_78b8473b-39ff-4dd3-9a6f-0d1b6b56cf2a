# Instrucciones para Corregir el Problema de Borrado

## Problema Identificado

Los botones de borrar no funcionan debido a:

1. **Error en función RPC**: La columna `pet_id` es ambigua en la función SQL
2. **Restricciones de clave foránea**: No se pueden borrar mascotas que tienen datos relacionados
3. **Políticas RLS**: Las políticas de seguridad pueden estar bloqueando el borrado

## Solución Implementada

He implementado **múltiples métodos de borrado** con fallbacks automáticos:

### Paso 1: Actualizar la función SQL en Supabase

Ve a tu panel de Supabase → SQL Editor y ejecuta este código:

```sql
-- Función para borrar una mascota y todos sus datos relacionados
CREATE OR REPLACE FUNCTION delete_pet_with_related_data(pet_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Borrar interacciones relacionadas
  DELETE FROM interactions WHERE pet_id = pet_id_param;
  
  -- Bo<PERSON>r mensajes relacionados
  DELETE FROM messages WHERE pet_id = pet_id_param;
  
  -- Borrar la mascota
  DELETE FROM pets WHERE id = pet_id_param;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    -- En caso de error, devolver FALSE
    RETURN FALSE;
END;
$$;
```

### Paso 2: Verificar que la función funciona

Ejecuta este código en el SQL Editor para probar:

```sql
-- Probar la función (esto no borrará nada real porque el ID no existe)
SELECT delete_pet_with_related_data('00000000-0000-0000-0000-000000000000');
```

### Paso 3: Los cambios en el código ya están aplicados

He actualizado el código para:

1. **Usar el nuevo parámetro** `pet_id_param` en lugar de `pet_id`
2. **Implementar borrado manual en cascada** como fallback
3. **Mejorar el manejo de errores**

## Cambios Realizados en el Código

### En `src/stores/petStore.js`:
- Función `deletePet` mejorada con borrado en cascada manual
- Mejor logging para depuración
- Manejo robusto de errores

### En `src/views/MisMascotas.vue`:
- Simplificada para usar solo la función del store
- Eliminadas funciones redundantes
- Mejor feedback al usuario

### En `src/views/Profile.vue`:
- Corregida la llamada a `fetchPets` con ID de usuario
- Mejor manejo de errores

## Métodos de Borrado Implementados

### 1. **Borrado Manual en Cascada** (Método Principal)
- Borra primero las interacciones
- Luego borra los mensajes
- Finalmente borra la mascota
- Incluye logging detallado para diagnóstico

### 2. **Función RPC como Fallback**
- Si el borrado manual falla, intenta usar la función RPC
- Prueba ambos nombres de parámetro (`pet_id_param` y `pet_id`)

### 3. **Método Alternativo Manual**
- Si todo falla, la aplicación ofrece un método alternativo
- El usuario puede elegir intentar con RPC directamente

## Cómo Probar

1. **Abre la consola del navegador** (F12) para ver logs detallados
2. **Ve a "Mis Mascotas"** y prueba borrar una mascota
3. **Ve a "Perfil"** y prueba borrar desde ahí también

### Si el Borrado Normal Falla:
- La aplicación te ofrecerá automáticamente usar el método alternativo
- Acepta para intentar con la función RPC
- Los logs te mostrarán exactamente qué está pasando

## Logs de Diagnóstico

En la consola verás logs como:
```
Intentando borrar mascota: [nombre] ID: [id]
Verificando datos existentes...
Interacciones encontradas: X
Mensajes encontrados: Y
Borrando interacciones...
Interacciones borradas exitosamente: X
Borrando mensajes...
Mensajes borradas exitosamente: Y
Verificando que no queden datos relacionados...
Interacciones restantes: 0
Mensajes restantes: 0
Borrando mascota...
Mascota borrada exitosamente
```

## Si Aún Hay Problemas

1. **Ejecuta la función SQL** en Supabase (Paso 1 arriba)
2. **Verifica las políticas RLS** en Supabase para las tablas `pets`, `interactions`, y `messages`
3. **Revisa los logs** en la consola para identificar el error específico
4. **Usa el método alternativo** cuando la aplicación lo ofrezca
