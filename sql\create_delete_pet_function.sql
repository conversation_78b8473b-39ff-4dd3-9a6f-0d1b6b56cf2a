-- Primero eliminar la función existente
DROP FUNCTION IF EXISTS delete_pet_with_related_data(uuid);

-- Crear la función corregida para borrar una mascota y todos sus datos relacionados
CREATE OR REPLACE FUNCTION delete_pet_with_related_data(pet_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Borrar interacciones relacionadas
  DELETE FROM interactions WHERE pet_id = pet_id_param;

  -- Bo<PERSON>r mensajes relacionados
  DELETE FROM messages WHERE pet_id = pet_id_param;

  -- Borrar la mascota
  DELETE FROM pets WHERE id = pet_id_param;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    -- En caso de error, devolver FALSE
    RETURN FALSE;
END;
$$;