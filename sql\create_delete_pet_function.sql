-- Función para borrar una mascota y todos sus datos relacionados
CREATE OR REPLACE FUNCTION delete_pet_with_related_data(pet_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Borrar interacciones relacionadas
  DELETE FROM interactions WHERE pet_id = $1;
  
  -- <PERSON><PERSON>r mensajes relacionados
  DELETE FROM messages WHERE pet_id = $1;
  
  -- <PERSON><PERSON>r la mascota
  DELETE FROM pets WHERE id = $1;
  
  RETURN TRUE;
END;
$$;