<template>
  <div class="pets-list-view">
    <h1><PERSON><PERSON></h1>
    <div v-if="loading">Cargando mascotas...</div>
    <div v-else-if="pets.length === 0">
      No tienes mascotas registradas.
      <div>
        <router-link to="/create-pet" class="create-pet-link">Crear una mascota</router-link>
      </div>
    </div>
    <ul v-else class="pets-list">
      <li v-for="pet in pets" :key="pet.id" class="pet-item">
        <div class="pet-main-row">
          <template v-if="editingId === pet.id">
            <input v-model="editName" :disabled="saving" class="edit-name-input" />
            <button @click="saveEdit(pet)" :disabled="saving || !editName.trim()">💾</button>
            <button @click="cancelEdit" :disabled="saving">✖️</button>
          </template>
          <template v-else>
            <span class="pet-name" @click="goToPet(pet)">{{ pet.name }}</span>
            <span class="pet-type">({{ pet.type }})</span>
            <button class="edit-btn" @click="startEdit(pet)">✏️</button>
            <button 
              class="delete-btn" 
              @click="handleDelete(pet)" 
              :disabled="saving"
              type="button"
            >🗑️</button>
          </template>
        </div>
        <div class="pet-status-row">
          <span>😊 {{ pet.happiness }}</span>
          <span>⚡ {{ pet.energy }}</span>
          <span>🍗 {{ pet.hunger }}</span>
          <span>🧼 {{ pet.cleanliness }}</span>
          <span>❤️ {{ pet.health }}</span>
        </div>
      </li>
    </ul>
    <div v-if="debug" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px;">
      <h3>Debug Info:</h3>
      <p><strong>Loading:</strong> {{ loading }}</p>
      <p><strong>Auth User:</strong> {{ authStore.user?.email || 'No user' }}</p>
      <p><strong>Auth User ID:</strong> {{ authStore.user?.id || 'No ID' }}</p>
      <p><strong>Store Pets Length:</strong> {{ petStore.pets?.length || 0 }}</p>
      <p><strong>Computed Pets Length:</strong> {{ pets.length }}</p>
      <pre>{{ JSON.stringify(pets, null, 2) }}</pre>
    </div>
    <!-- Modal de confirmación de borrado -->
    <div v-if="showConfirmDialog" class="modal-overlay" @click="closeConfirmDialog">
      <div class="modal-dialog confirm-modal" @click.stop>
        <div class="modal-header">
          <h3>⚠️ Confirmar eliminación</h3>
        </div>
        <div class="modal-body">
          <p>{{ confirmMessage }}</p>
        </div>
        <div class="modal-footer">
          <button @click="confirmDelete(false)" class="btn btn-secondary">Cancelar</button>
          <button @click="confirmDelete(true)" class="btn btn-danger">Sí, eliminar</button>
        </div>
      </div>
    </div>

    <!-- Modal de éxito -->
    <div v-if="showSuccessDialog" class="modal-overlay" @click="closeSuccessDialog">
      <div class="modal-dialog success-modal" @click.stop>
        <div class="modal-header">
          <h3>✅ Operación exitosa</h3>
        </div>
        <div class="modal-body">
          <p>{{ successMessage }}</p>
        </div>
        <div class="modal-footer">
          <button @click="closeSuccessDialog" class="btn btn-primary">Aceptar</button>
        </div>
      </div>
    </div>

    <!-- Modal de error -->
    <div v-if="showErrorDialog" class="modal-overlay" @click="closeErrorDialog">
      <div class="modal-dialog error-modal" @click.stop>
        <div class="modal-header">
          <h3>❌ Error</h3>
        </div>
        <div class="modal-body">
          <p>{{ errorMessage }}</p>
        </div>
        <div class="modal-footer">
          <button @click="closeErrorDialog" class="btn btn-secondary">Cerrar</button>
        </div>
      </div>
    </div>

    <!-- Modal de reintentar -->
    <div v-if="showRetryDialog" class="modal-overlay" @click="closeRetryDialog">
      <div class="modal-dialog retry-modal" @click.stop>
        <div class="modal-header">
          <h3>🔄 Método alternativo</h3>
        </div>
        <div class="modal-body">
          <p>{{ retryMessage }}</p>
          <p><small>¿Quieres intentar con el método alternativo (RPC)?</small></p>
        </div>
        <div class="modal-footer">
          <button @click="confirmRetry(false)" class="btn btn-secondary">No, cancelar</button>
          <button @click="confirmRetry(true)" class="btn btn-warning">Sí, intentar</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeMount, computed } from 'vue';
import { usePetStore } from '../stores/petStore';
import { useAuthStore } from '../stores/authStore';
import { useRouter } from 'vue-router';
import { supabase } from '../services/supabase';

const petStore = usePetStore();
const authStore = useAuthStore();
const router = useRouter();

// Usar directamente la referencia del store
const loading = ref(false);
const editingId = ref(null);
const editName = ref('');
const saving = ref(false);

// Variables para los modales
const showConfirmDialog = ref(false);
const showSuccessDialog = ref(false);
const showErrorDialog = ref(false);
const showRetryDialog = ref(false);

const confirmMessage = ref('');
const successMessage = ref('');
const errorMessage = ref('');
const retryMessage = ref('');

const petToDelete = ref(null);
const pendingAction = ref(null);

// Añadir para depuración
const debug = false; // Cambiar a true para ver los datos en la UI

// Usar directamente la referencia reactiva del store
const pets = computed(() => petStore.pets || []);



onMounted(async () => {
  console.log('=== MisMascotas onMounted ===');
  console.log('Auth store user:', authStore.user);

  if (!authStore.user) {
    console.log('No hay usuario autenticado, redirigiendo a login');
    router.push({ name: 'Login' });
    return;
  }

  loading.value = true;
  try {
    console.log('Cargando mascotas para usuario:', authStore.user.id);
    console.log('Estado del store antes de fetchPets:', {
      pets: petStore.pets,
      loading: petStore.loading,
      error: petStore.error
    });

    await petStore.fetchPets(authStore.user.id);

    console.log('Estado del store después de fetchPets:', {
      pets: petStore.pets,
      petsLength: petStore.pets?.length,
      loading: petStore.loading,
      error: petStore.error
    });

    console.log('Computed pets:', pets.value);
    console.log('Computed pets length:', pets.value.length);

  } catch (error) {
    console.error('Error fetching pets:', error);
  } finally {
    loading.value = false;
    console.log('=== Fin de onMounted ===');
  }
});

function goToPet(pet) {
  petStore.setCurrentPet(pet);
  router.push({ name: 'PetInteraction', params: { id: pet.id } });
}

function startEdit(pet) {
  editingId.value = pet.id;
  editName.value = pet.name;
}

async function saveEdit(pet) {
  if (!editName.value.trim()) return;
  saving.value = true;
  try {
    const success = await petStore.updatePetName(pet.id, editName.value.trim());
    if (success) {
      // Recargar la lista para asegurar que está actualizada
      await petStore.fetchPets(authStore.user.id);
    }
  } catch (error) {
    console.error('Error updating pet name:', error);
  } finally {
    editingId.value = null;
    saving.value = false;
  }
}

function cancelEdit() {
  editingId.value = null;
}

// Funciones para manejar los modales
function showConfirmModal(message, pet) {
  console.log('=== showConfirmModal llamado ===');
  console.log('Message:', message);
  console.log('Pet recibido:', pet);
  console.log('Pet type:', typeof pet);
  console.log('Pet keys:', pet ? Object.keys(pet) : 'null');

  confirmMessage.value = message;

  // Hacer una copia profunda del objeto para evitar problemas de reactividad
  try {
    petToDelete.value = JSON.parse(JSON.stringify(pet));
    console.log('Copia profunda creada exitosamente');
  } catch (error) {
    console.error('Error al crear copia profunda, usando referencia directa:', error);
    petToDelete.value = pet;
  }

  showConfirmDialog.value = true;

  console.log('petToDelete.value después de asignar:', petToDelete.value);
  console.log('showConfirmDialog.value:', showConfirmDialog.value);
}

function closeConfirmDialog() {
  console.log('=== closeConfirmDialog llamado ===');
  console.log('petToDelete.value antes de limpiar:', petToDelete.value);

  showConfirmDialog.value = false;
  petToDelete.value = null;

  console.log('Modal cerrado y petToDelete limpiado');
}

function showSuccessModal(message) {
  successMessage.value = message;
  showSuccessDialog.value = true;
}

function closeSuccessDialog() {
  showSuccessDialog.value = false;
}

function showErrorModal(message) {
  errorMessage.value = message;
  showErrorDialog.value = true;
}

function closeErrorDialog() {
  showErrorDialog.value = false;
}

function showRetryModal(message, action) {
  retryMessage.value = message;
  pendingAction.value = action;
  showRetryDialog.value = true;
}

function closeRetryDialog() {
  showRetryDialog.value = false;
  pendingAction.value = null;
}

async function confirmRetry(retry) {
  closeRetryDialog();
  if (retry && pendingAction.value) {
    await pendingAction.value();
  }
}



// Función para manejar el clic en el botón de borrado
function handleDelete(pet) {
  console.log('=== handleDelete llamado ===');
  console.log('Pet a borrar:', pet);
  console.log('Pet ID:', pet.id);
  console.log('Pet name:', pet.name);

  showConfirmModal(
    `¿Seguro que quieres borrar a ${pet.name}? Esta acción no se puede deshacer.`,
    pet
  );

  console.log('Modal de confirmación mostrado');
}

// Función que se ejecuta cuando se confirma el borrado
async function confirmDelete(confirmed) {
  console.log('=== confirmDelete llamado ===');
  console.log('Confirmed:', confirmed);
  console.log('petToDelete.value:', petToDelete.value);
  console.log('petToDelete.value type:', typeof petToDelete.value);
  console.log('petToDelete.value keys:', petToDelete.value ? Object.keys(petToDelete.value) : 'null');

  // Guardar una copia del pet ANTES de cerrar el modal
  const pet = petToDelete.value;

  closeConfirmDialog();

  if (!confirmed) {
    console.log('Borrado cancelado por el usuario');
    return;
  }

  if (!pet || !pet.id) {
    console.log('No hay mascota seleccionada o falta ID');
    console.log('pet:', pet);
    return;
  }
  saving.value = true;

  console.log('Iniciando proceso de borrado...');

  try {
    console.log('Intentando borrar mascota:', pet.name, 'ID:', pet.id);
    const success = await petStore.deletePet(pet.id);
    console.log('Resultado de petStore.deletePet:', success);

    if (success) {
      // Recargar la lista desde Supabase para asegurar sincronización
      await petStore.fetchPets(authStore.user.id);
      showSuccessModal(`${pet.name} ha sido eliminado correctamente.`);
    } else {
      console.error('Borrado falló, error del store:', petStore.error);

      // Ofrecer método alternativo usando modal
      const retryAction = async () => {
        saving.value = true;
        try {
          console.log('Intentando borrado con RPC...');
          const rpcSuccess = await petStore.deletePetWithRPC(pet.id);
          if (rpcSuccess) {
            await petStore.fetchPets(authStore.user.id);
            showSuccessModal(`${pet.name} ha sido eliminado correctamente usando método alternativo.`);
          } else {
            showErrorModal('Error al borrar la mascota con método alternativo: ' + (petStore.error || 'Error desconocido.'));
          }
        } catch (error) {
          console.error('Error en método alternativo:', error);
          showErrorModal('Error al borrar la mascota con método alternativo: ' + error.message);
        } finally {
          saving.value = false;
        }
      };

      showRetryModal(
        `El borrado normal falló: ${petStore.error || 'Error desconocido'}`,
        retryAction
      );
    }
  } catch (error) {
    console.error('Error al borrar mascota:', error);
    showErrorModal('Error al borrar la mascota: ' + error.message);
  } finally {
    saving.value = false;
  }
}


</script>

<style scoped>
.pets-list-view {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.pets-list {
  list-style: none;
  padding: 0;
}
.pet-item {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}
.pet-item a {
  text-decoration: none;
  color: #2c3e50;
  transition: color 0.2s;
}
.pet-item a:hover {
  color: #42b983;
}
.pet-main-row {
  display: flex;
  align-items: center;
  gap: 0.5em;
}
.pet-name {
  font-weight: bold;
  color: #2c3e50;
  cursor: pointer;
  margin-right: 0.5em;
}
.pet-type {
  color: #888;
  margin-right: 0.5em;
}
.edit-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1em;
  color: #888;
  margin-left: 0.2em;
}
.edit-btn:hover {
  color: #42b983;
}
.edit-name-input {
  font-size: 1em;
  padding: 0.2em 0.5em;
  border-radius: 4px;
  border: 1px solid #ccc;
}
.pet-status-row {
  display: flex;
  gap: 1em;
  font-size: 0.95em;
  color: #555;
  margin-left: 1.5em;
  margin-top: 0.2em;
}
.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1em;
  color: #e74c3c;
  margin-left: 0.2em;
  padding: 5px;  /* Añadir padding para aumentar el área clickeable */
}
.delete-btn:hover {
  color: #c0392b;
}
.delete-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}
.create-pet-link {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #42b983;
  color: white;
  text-decoration: none;
  border-radius: 4px;
}
.create-pet-link:hover {
  background-color: #3aa876;
}

/* Estilos para los modales */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.modal-dialog {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 450px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

.modal-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-body {
  padding: 20px 24px;
  line-height: 1.6;
}

.modal-body p {
  margin: 0 0 12px 0;
}

.modal-body small {
  color: #6c757d;
  font-style: italic;
}

.modal-footer {
  padding: 16px 24px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

/* Botones de los modales */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background-color: #42b983;
  color: white;
}

.btn-primary:hover {
  background-color: #369870;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-danger {
  background-color: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background-color: #c0392b;
}

.btn-warning {
  background-color: #f39c12;
  color: white;
}

.btn-warning:hover {
  background-color: #e67e22;
}

/* Estilos específicos por tipo de modal */
.confirm-modal .modal-header {
  background-color: #fff3cd;
  border-bottom-color: #ffeaa7;
}

.success-modal .modal-header {
  background-color: #d4edda;
  border-bottom-color: #c3e6cb;
}

.error-modal .modal-header {
  background-color: #f8d7da;
  border-bottom-color: #f5c6cb;
}

.retry-modal .modal-header {
  background-color: #e2e3e5;
  border-bottom-color: #d6d8db;
}

/* Animaciones */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive */
@media (max-width: 480px) {
  .modal-dialog {
    width: 95%;
    margin: 20px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}
</style>
