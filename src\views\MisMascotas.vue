<template>
  <div class="pets-list-view">
    <h1><PERSON><PERSON></h1>
    <div v-if="loading">Cargando mascotas...</div>
    <div v-else-if="pets.length === 0">
      No tienes mascotas registradas.
      <div>
        <router-link to="/create-pet" class="create-pet-link">Crear una mascota</router-link>
      </div>
    </div>
    <ul v-else class="pets-list">
      <li v-for="pet in pets" :key="pet.id" class="pet-item">
        <div class="pet-main-row">
          <template v-if="editingId === pet.id">
            <input v-model="editName" :disabled="saving" class="edit-name-input" />
            <button @click="saveEdit(pet)" :disabled="saving || !editName.trim()">💾</button>
            <button @click="cancelEdit" :disabled="saving">✖️</button>
          </template>
          <template v-else>
            <span class="pet-name" @click="goToPet(pet)">{{ pet.name }}</span>
            <span class="pet-type">({{ pet.type }})</span>
            <button class="edit-btn" @click="startEdit(pet)">✏️</button>
            <button 
              class="delete-btn" 
              @click="handleDelete(pet)" 
              :disabled="saving"
              type="button"
            >🗑️</button>
          </template>
        </div>
        <div class="pet-status-row">
          <span>😊 {{ pet.happiness }}</span>
          <span>⚡ {{ pet.energy }}</span>
          <span>🍗 {{ pet.hunger }}</span>
          <span>🧼 {{ pet.cleanliness }}</span>
          <span>❤️ {{ pet.health }}</span>
        </div>
      </li>
    </ul>
    <pre v-if="debug">{{ JSON.stringify(pets, null, 2) }}</pre>
    <!-- Diálogo de confirmación personalizado -->
    <div v-if="showConfirmDialog" class="confirm-dialog-overlay">
      <div class="confirm-dialog">
        <p>{{ confirmMessage }}</p>
        <div class="confirm-buttons">
          <button @click="confirmDelete(true)" class="confirm-yes">Sí, borrar</button>
          <button @click="confirmDelete(false)" class="confirm-no">Cancelar</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeMount } from 'vue';
import { usePetStore } from '../stores/petStore';
import { useAuthStore } from '../stores/authStore';
import { useRouter } from 'vue-router';
import { supabase } from '../services/supabase';

const petStore = usePetStore();
const authStore = useAuthStore();
const router = useRouter();

// Usar directamente la referencia del store
const loading = ref(false);
const pets = ref([]);
const editingId = ref(null);
const editName = ref('');
const saving = ref(false);

// Variables para el diálogo de confirmación
const showConfirmDialog = ref(false);
const confirmMessage = ref('');
const petToDelete = ref(null);

// Añadir para depuración
const debug = false; // Cambiar a true para ver los datos en la UI

// Mantener un conjunto de IDs de mascotas borradas localmente
const deletedPetIds = ref(new Set());

// Cargar IDs de mascotas borradas desde localStorage
onBeforeMount(() => {
  const savedDeletedIds = localStorage.getItem('deletedPetIds');
  if (savedDeletedIds) {
    try {
      const idsArray = JSON.parse(savedDeletedIds);
      deletedPetIds.value = new Set(idsArray);
      console.log('IDs de mascotas borradas cargados:', Array.from(deletedPetIds.value));
    } catch (e) {
      console.error('Error al cargar IDs de mascotas borradas:', e);
    }
  }
});

// Guardar IDs de mascotas borradas en localStorage
function saveDeletedPetIds() {
  try {
    const idsArray = Array.from(deletedPetIds.value);
    localStorage.setItem('deletedPetIds', JSON.stringify(idsArray));
    console.log('IDs de mascotas borradas guardados:', idsArray);
  } catch (e) {
    console.error('Error al guardar IDs de mascotas borradas:', e);
  }
}

onMounted(async () => {
  if (!authStore.user) {
    router.push({ name: 'Login' });
    return;
  }
  
  loading.value = true;
  try {
    await petStore.fetchPets(authStore.user.id);
    // Filtrar mascotas borradas localmente usando el conjunto de IDs
    pets.value = petStore.pets.filter(p => !deletedPetIds.value.has(p.id));
    console.log('Mascotas cargadas (filtradas):', pets.value);
  } catch (error) {
    console.error('Error fetching pets:', error);
    pets.value = [];
  } finally {
    loading.value = false;
  }
  console.log('Estado del store después de montar:', {
    'petStore.pets': petStore.pets,
    'pets.value': pets.value
  });
});

function goToPet(pet) {
  petStore.setCurrentPet(pet);
  router.push({ name: 'PetInteraction', params: { id: pet.id } });
}

function startEdit(pet) {
  editingId.value = pet.id;
  editName.value = pet.name;
}

async function saveEdit(pet) {
  if (!editName.value.trim()) return;
  saving.value = true;
  try {
    const success = await petStore.updatePetName(pet.id, editName.value.trim());
    if (success) {
      // Actualizar la lista completa para asegurar reactividad
      await petStore.fetchPets(authStore.user.id);
      // Importante: petStore.pets es una referencia directa, no tiene .value
      pets.value = [...petStore.pets];
    }
  } catch (error) {
    console.error('Error updating pet name:', error);
  } finally {
    editingId.value = null;
    saving.value = false;
  }
}

function cancelEdit() {
  editingId.value = null;
}



// Función para manejar el clic en el botón de borrado
async function handleDelete(pet) {
  if (confirm(`¿Seguro que quieres borrar a ${pet.name}? Esta acción no se puede deshacer.`)) {
    saving.value = true;
    try {
      const success = await petStore.deletePet(pet.id);
      if (success) {
        // Actualizar la lista local inmediatamente
        pets.value = pets.value.filter(p => p.id !== pet.id);
        alert(`${pet.name} ha sido eliminado correctamente.`);
      } else {
        alert('Error al borrar la mascota: ' + (petStore.error || 'Error desconocido.'));
      }
    } catch (error) {
      console.error('Error al borrar mascota:', error);
      alert('Error al borrar la mascota: ' + error.message);
    } finally {
      saving.value = false;
    }
  }
}


</script>

<style scoped>
.pets-list-view {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.pets-list {
  list-style: none;
  padding: 0;
}
.pet-item {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}
.pet-item a {
  text-decoration: none;
  color: #2c3e50;
  transition: color 0.2s;
}
.pet-item a:hover {
  color: #42b983;
}
.pet-main-row {
  display: flex;
  align-items: center;
  gap: 0.5em;
}
.pet-name {
  font-weight: bold;
  color: #2c3e50;
  cursor: pointer;
  margin-right: 0.5em;
}
.pet-type {
  color: #888;
  margin-right: 0.5em;
}
.edit-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1em;
  color: #888;
  margin-left: 0.2em;
}
.edit-btn:hover {
  color: #42b983;
}
.edit-name-input {
  font-size: 1em;
  padding: 0.2em 0.5em;
  border-radius: 4px;
  border: 1px solid #ccc;
}
.pet-status-row {
  display: flex;
  gap: 1em;
  font-size: 0.95em;
  color: #555;
  margin-left: 1.5em;
  margin-top: 0.2em;
}
.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1em;
  color: #e74c3c;
  margin-left: 0.2em;
  padding: 5px;  /* Añadir padding para aumentar el área clickeable */
}
.delete-btn:hover {
  color: #c0392b;
}
.delete-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}
.create-pet-link {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #42b983;
  color: white;
  text-decoration: none;
  border-radius: 4px;
}
.create-pet-link:hover {
  background-color: #3aa876;
}

/* Estilos para el diálogo de confirmación */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirm-dialog {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 100%;
}

.confirm-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-yes {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-no {
  background-color: #95a5a6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
